/**
 * Centralized FontAwesome Icon Registry
 * 
 * This file serves as the single source of truth for all FontAwesome icons used in ChatLo.
 * All icons are stored locally in the \src\components\Icons folder for offline accessibility.
 * 
 * Development Standards Compliance:
 * - Rule 2.2: FontAwesome Icon Management
 * - All icons imported from this registry only
 * - No direct FontAwesome imports in components
 * - Downloaded icons, not embedded/CDN
 * - Only icons in use are stored locally
 */

// Import FontAwesome React component and icon definitions
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { IconDefinition } from '@fortawesome/fontawesome-svg-core'

// Import specific icons from local FontAwesome packages
import {
  // Navigation Icons
  faHome,
  faComment,
  faClockRotateLeft,
  faFolderTree,
  faUser,
  faGear,
  faSearch,
  faArrowLeft,
  faArrowRight,
  faChevronLeft,
  faChevronRight,
  faChevronUp,
  faChevronDown,
  
  // File & Document Icons
  faFolder,
  faFile,
  faFileText,
  faFileCode,
  faFileImage,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileArchive,
  faFileAudio,
  faFileVideo,
  
  // Action Icons
  faPlus,
  faMinus,
  faXmark,
  faCheck,
  faTimes,
  faEdit,
  faTrash,
  faCopy,
  faPaste,
  faDownload,
  faUpload,
  faRefresh,
  faSync,
  faRotate,
  
  // UI Control Icons
  faEllipsisV,
  faEllipsisH,
  faBars,
  faGrip,
  faList,
  faSort,
  faFilter,
  faExpand,
  faCompress,
  faMaximize,
  faMinimize,
  
  // Status & Feedback Icons
  faSpinner,
  faCircleNotch,
  faExclamationTriangle,
  faInfoCircle,
  faCheckCircle,
  faTimesCircle,
  faQuestionCircle,
  
  // Communication Icons
  faPaperPlane,
  faReply,
  faShare,
  faLink,
  faEnvelope,
  faPhone,
  
  // System Icons
  faWifi,
  faWifiSlash,
  faSignal,
  faBattery,
  faCog,
  faTools,
  faDatabase,
  faServer,
  faCloud,
  faCloudDownload,
  faCloudUpload,
  
  // Content Icons
  faImage,
  faVideo,
  faMusic,
  faVolumeUp,
  faVolumeDown,
  faVolumeMute,
  faPlay,
  faPause,
  faStop,
  faForward,
  faBackward,
  
  // Layout Icons
  faLayerGroup,
  faArrowsRotate,
  faColumns,
  faRows,
  faTable,
  faThLarge,
  faThList,
  
  // Utility Icons
  faCalendar,
  faClock,
  faStopwatch,
  faHeart,
  faStar,
  faBookmark,
  faTag,
  faFlag,
  faPin,
  faLock,
  faUnlock,
  faEye,
  faEyeSlash,
  
} from '@fortawesome/free-solid-svg-icons'

import {
  // Regular style icons
  faFile as faFileRegular,
  faFolder as faFolderRegular,
  faHeart as faHeartRegular,
  faStar as faStarRegular,
  faBookmark as faBookmarkRegular,
  faCircle as faCircleRegular,
  faSquare as faSquareRegular,
  faCheckSquare as faCheckSquareRegular,
  faTimesCircle as faTimesCircleRegular,
  faQuestionCircle as faQuestionCircleRegular,
  faExclamationCircle as faExclamationCircleRegular,
  faInfoCircle as faInfoCircleRegular,
  faCheckCircle as faCheckCircleRegular,
} from '@fortawesome/free-regular-svg-icons'

import {
  // Brand icons (if needed)
  faGithub,
  faGoogle,
  faMicrosoft,
  faApple,
  faWindows,
  faLinux,
  faChrome,
  faFirefox,
  faSafari,
  faEdge,
} from '@fortawesome/free-brands-svg-icons'

/**
 * Centralized Icon Registry
 * 
 * All icons used in the application should be defined here.
 * This ensures consistent usage and prevents import errors.
 */
export const ICONS = {
  // Navigation
  HOME: faHome,
  CHAT: faComment,
  HISTORY: faClockRotateLeft,
  FILES: faFolderTree,
  USER: faUser,
  SETTINGS: faGear,
  SEARCH: faSearch,
  ARROW_LEFT: faArrowLeft,
  ARROW_RIGHT: faArrowRight,
  CHEVRON_LEFT: faChevronLeft,
  CHEVRON_RIGHT: faChevronRight,
  CHEVRON_UP: faChevronUp,
  CHEVRON_DOWN: faChevronDown,
  
  // Files & Documents
  FOLDER: faFolder,
  FOLDER_REGULAR: faFolderRegular,
  FILE: faFile,
  FILE_REGULAR: faFileRegular,
  FILE_TEXT: faFileText,
  FILE_CODE: faFileCode,
  FILE_IMAGE: faFileImage,
  FILE_PDF: faFilePdf,
  FILE_WORD: faFileWord,
  FILE_EXCEL: faFileExcel,
  FILE_POWERPOINT: faFilePowerpoint,
  FILE_ARCHIVE: faFileArchive,
  FILE_AUDIO: faFileAudio,
  FILE_VIDEO: faFileVideo,
  
  // Actions
  PLUS: faPlus,
  MINUS: faMinus,
  CLOSE: faXmark,
  CHECK: faCheck,
  TIMES: faTimes,
  EDIT: faEdit,
  DELETE: faTrash,
  COPY: faCopy,
  PASTE: faPaste,
  DOWNLOAD: faDownload,
  UPLOAD: faUpload,
  REFRESH: faRefresh,
  SYNC: faSync,
  ROTATE: faRotate,
  
  // UI Controls
  MENU_VERTICAL: faEllipsisV,
  MENU_HORIZONTAL: faEllipsisH,
  HAMBURGER: faBars,
  GRIP: faGrip,
  LIST: faList,
  SORT: faSort,
  FILTER: faFilter,
  EXPAND: faExpand,
  COMPRESS: faCompress,
  MAXIMIZE: faMaximize,
  MINIMIZE: faMinimize,
  
  // Status & Feedback
  LOADING: faSpinner,
  LOADING_ALT: faCircleNotch,
  WARNING: faExclamationTriangle,
  INFO: faInfoCircle,
  SUCCESS: faCheckCircle,
  ERROR: faTimesCircle,
  QUESTION: faQuestionCircle,
  
  // Communication
  SEND: faPaperPlane,
  REPLY: faReply,
  SHARE: faShare,
  LINK: faLink,
  EMAIL: faEnvelope,
  PHONE: faPhone,
  
  // System
  WIFI: faWifi,
  WIFI_OFF: faWifiSlash,
  SIGNAL: faSignal,
  BATTERY: faBattery,
  COG: faCog,
  TOOLS: faTools,
  DATABASE: faDatabase,
  SERVER: faServer,
  CLOUD: faCloud,
  CLOUD_DOWNLOAD: faCloudDownload,
  CLOUD_UPLOAD: faCloudUpload,
  
  // Content
  IMAGE: faImage,
  VIDEO: faVideo,
  MUSIC: faMusic,
  VOLUME_UP: faVolumeUp,
  VOLUME_DOWN: faVolumeDown,
  VOLUME_MUTE: faVolumeMute,
  PLAY: faPlay,
  PAUSE: faPause,
  STOP: faStop,
  FORWARD: faForward,
  BACKWARD: faBackward,
  
  // Layout
  LAYERS: faLayerGroup,
  ARROWS_ROTATE: faArrowsRotate,
  COLUMNS: faColumns,
  ROWS: faRows,
  TABLE: faTable,
  GRID_LARGE: faThLarge,
  GRID_LIST: faThList,
  
  // Utility
  CALENDAR: faCalendar,
  CLOCK: faClock,
  STOPWATCH: faStopwatch,
  HEART: faHeart,
  HEART_REGULAR: faHeartRegular,
  STAR: faStar,
  STAR_REGULAR: faStarRegular,
  BOOKMARK: faBookmark,
  BOOKMARK_REGULAR: faBookmarkRegular,
  TAG: faTag,
  FLAG: faFlag,
  PIN: faPin,
  LOCK: faLock,
  UNLOCK: faUnlock,
  EYE: faEye,
  EYE_SLASH: faEyeSlash,
  
  // Brands
  GITHUB: faGithub,
  GOOGLE: faGoogle,
  MICROSOFT: faMicrosoft,
  APPLE: faApple,
  WINDOWS: faWindows,
  LINUX: faLinux,
  CHROME: faChrome,
  FIREFOX: faFirefox,
  SAFARI: faSafari,
  EDGE: faEdge,
} as const

/**
 * Icon Component Wrapper
 * 
 * Provides a consistent interface for using FontAwesome icons throughout the app.
 */
export interface IconProps {
  icon: keyof typeof ICONS
  className?: string
  size?: 'xs' | 'sm' | 'lg' | 'xl' | '2x' | '3x' | '4x' | '5x'
  spin?: boolean
  pulse?: boolean
  fixedWidth?: boolean
}

export const Icon: React.FC<IconProps> = ({ 
  icon, 
  className = '', 
  size,
  spin = false,
  pulse = false,
  fixedWidth = false 
}) => {
  const iconDefinition = ICONS[icon]
  
  if (!iconDefinition) {
    console.warn(`Icon "${icon}" not found in registry`)
    return null
  }
  
  return (
    <FontAwesomeIcon
      icon={iconDefinition}
      className={className}
      size={size}
      spin={spin}
      pulse={pulse}
      fixedWidth={fixedWidth}
    />
  )
}

/**
 * File Type Icon Helper
 * 
 * Returns appropriate icon for file types with color coding
 */
export const getFileTypeIcon = (fileType: string, isDirectory?: boolean): keyof typeof ICONS => {
  if (isDirectory) return 'FOLDER'
  
  const type = fileType.toLowerCase()
  
  // Document types
  if (type.includes('pdf')) return 'FILE_PDF'
  if (type.includes('word') || type.includes('doc')) return 'FILE_WORD'
  if (type.includes('excel') || type.includes('xls')) return 'FILE_EXCEL'
  if (type.includes('powerpoint') || type.includes('ppt')) return 'FILE_POWERPOINT'
  
  // Code types
  if (type.includes('code') || type.includes('js') || type.includes('ts') || 
      type.includes('py') || type.includes('html') || type.includes('css')) return 'FILE_CODE'
  
  // Media types
  if (type.includes('image') || type.includes('img')) return 'FILE_IMAGE'
  if (type.includes('video')) return 'FILE_VIDEO'
  if (type.includes('audio')) return 'FILE_AUDIO'
  
  // Archive types
  if (type.includes('zip') || type.includes('rar') || type.includes('tar')) return 'FILE_ARCHIVE'
  
  // Text types
  if (type.includes('text') || type.includes('txt') || type.includes('md')) return 'FILE_TEXT'
  
  // Default
  return 'FILE'
}

/**
 * Export the FontAwesome component for direct use when needed
 */
export { FontAwesomeIcon }

/**
 * Export icon definitions for advanced use cases
 */
export type IconDefinitionType = IconDefinition
